const axios = require('axios');

exports.main = async (context) => {
  try {
    console.log('RMS Data serverless function called with context:', context);

    // Extract parameters from the context
    const {
      userId,
      userEmail,
      associatedObjectId,
      associatedObjectType = 'CONTACT',
      portalId,
      firstname = '',
      phone = '',
      email = '',
      lastname = ''
    } = context.parameters || {};

    console.log('Extracted parameters:', {
      userId, userEmail, associatedObjectId, associatedObjectType,
      portalId, firstname, phone, email, lastname
    });

    // Validate required parameters
    if (!userId || !userEmail || !portalId) {
      const errorMsg = 'Missing required parameters: userId, userEmail, or portalId';
      console.error(errorMsg);
      return {
        statusCode: 400,
        body: { error: errorMsg },
        headers: {
          'Content-Type': 'application/json',
        },
      };
    }

    // Build the API URL with query parameters
    const baseUrl = 'https://api.niswey.net/rmscrmseries/api/hubspot/crm';
    const params = new URLSearchParams({
      userId,
      userEmail,
      associatedObjectId: associatedObjectId || '',
      associatedObjectType,
      portalId,
      firstname,
      phone,
      email,
      lastname
    });

    const apiUrl = `${baseUrl}?${params.toString()}`;

    console.log('Making GET request to RMS API:', apiUrl);

    // Make GET request to the RMS API
    const response = await axios.get(apiUrl, {
      timeout: 10000, // 10 second timeout
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'HubSpot-RMS-Integration/1.0'
      }
    });

    // Log the successful response
    console.log('RMS API call successful, data received:', response.data);

    // Return a properly formatted response with status code and body
    return {
      statusCode: 200,
      body: response.data,
      headers: {
        'Content-Type': 'application/json',
      },
    };

  } catch (error) {
    // Handle any errors that occur during the request
    console.error('Error fetching RMS data:', error.message);

    let errorMessage = 'Failed to fetch RMS data';
    let statusCode = 500;

    if (error.response) {
      // API responded with error status
      statusCode = error.response.status;
      errorMessage = `RMS API Error: ${error.response.status} - ${error.response.statusText}`;
      console.error('RMS API Response Error:', error.response.data);
    } else if (error.request) {
      // Request was made but no response received
      errorMessage = 'No response received from RMS API';
      console.error('No response received:', error.request);
    }

    // Return an error response
    return {
      statusCode: statusCode,
      body: { error: errorMessage },
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};