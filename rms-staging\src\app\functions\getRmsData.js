// Using HubSpot's built-in httpClient instead of axios

exports.main = async (context = {}) => {
  try {
    console.log('Serverless function called with context:', context);

    // Extract parameters from the context
    const {
      userId,
      userEmail,
      associatedObjectId,
      associatedObjectType = 'CONTACT',
      portalId,
      firstname = '',
      phone = '',
      email = '',
      lastname = ''
    } = context.parameters || {};

    console.log('Extracted parameters:', {
      userId, userEmail, associatedObjectId, associatedObjectType,
      portalId, firstname, phone, email, lastname
    });

    // Validate required parameters
    if (!userId || !userEmail || !portalId) {
      const errorMsg = 'Missing required parameters: userId, userEmail, or portalId';
      console.error(errorMsg);
      return {
        statusCode: 400,
        body: {
          success: false,
          error: errorMsg
        }
      };
    }

    // Build the API URL with query parameters
    const baseUrl = 'https://api.niswey.net/rmscrmseries/api/hubspot/crm';
    const params = new URLSearchParams({
      userId,
      userEmail,
      associatedObjectId: associatedObjectId || '',
      associatedObjectType,
      portalId,
      firstname,
      phone,
      email,
      lastname
    });

    const apiUrl = `${baseUrl}?${params.toString()}`;

    console.log('Making API request to:', apiUrl);

    
    // Make the API request
    const response = await context.httpClient.get(apiUrl, {
      timeout: 10000, // 10 second timeout
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'HubSpot-RMS-Integration/1.0'
      }
    });

    // Return the successful response
    console.log('API call successful, returning data');
    return {
      statusCode: 200,
      body: {
        success: true,
        data: response.data
      }
    };

  } catch (error) {
    console.error('Error in get-rms-data function:', error);

    // Handle different types of errors
    let errorMessage = 'Unknown error occurred';
    let statusCode = 500;

    if (error.response) {
      // API responded with error status
      statusCode = error.response.status;
      errorMessage = `API Error: ${error.response.status} - ${error.response.statusText}`;
      console.error('API Response Error:', error.response.data);
    } else if (error.request) {
      // Request was made but no response received
      errorMessage = 'No response received from API';
      console.error('No response received:', error.request);
    } else {
      // Something else happened
      errorMessage = error.message;
      console.error('Request setup error:', error.message);
    }

    return {
      statusCode: statusCode,
      body: {
        success: false,
        error: errorMessage,
        details: error.response?.data || null
      }
    };
  }
};
